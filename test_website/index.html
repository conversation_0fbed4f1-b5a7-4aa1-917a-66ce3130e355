<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebScan敏感信息提取测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            color: #333;
            margin-top: 0;
        }
        .info-item {
            background: #f8f9fa;
            padding: 8px;
            margin: 5px 0;
            border-radius: 3px;
            font-family: monospace;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebScan敏感信息提取测试页面</h1>
        <p>本页面包含各种类型的敏感信息，用于测试WebScan与FindSomething的兼容性。</p>
        
        <div class="section">
            <h3>📧 邮箱地址</h3>
            <div class="info-item">联系邮箱: "<EMAIL>"</div>
            <div class="info-item">技术支持: '<EMAIL>'</div>
            <div class="info-item">商务合作: "<EMAIL>"</div>
        </div>
        
        <div class="section">
            <h3>📱 手机号码</h3>
            <div class="info-item">客服热线: "13812345678"</div>
            <div class="info-item">技术支持: '15987654321'</div>
            <div class="info-item">紧急联系: "18666888999"</div>
        </div>
        
        <div class="section">
            <h3>🆔 身份证号</h3>
            <div class="info-item">测试身份证: "110101199001011234"</div>
            <div class="info-item">示例证件: '320102198505050987'</div>
        </div>
        
        <div class="section">
            <h3>🌐 域名和URL</h3>
            <div class="info-item">官网: "https://www.example.com"</div>
            <div class="info-item">API地址: 'https://api.test.org/v1'</div>
            <div class="info-item">CDN: "cdn.static.net"</div>
            <div class="info-item">备用域名: 'backup.site.cn'</div>
        </div>
        
        <div class="section">
            <h3>🖥️ IP地址</h3>
            <div class="info-item">服务器IP: "*************"</div>
            <div class="info-item">数据库IP: '*********:3306'</div>
            <div class="info-item">Redis: "127.0.0.1:6379"</div>
            <div class="info-item">外网IP: 'https://************/api'</div>
        </div>
        
        <div class="section">
            <h3>🔐 密钥和令牌</h3>
            <div class="info-item">API密钥: "sk_test_1234567890abcdef"</div>
            <div class="info-item">访问令牌: 'access_token_xyz789'</div>
            <div class="info-item">JWT: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"</div>
        </div>
        
        <div class="section">
            <h3>📁 路径信息</h3>
            <div class="info-item">配置路径: "/etc/nginx/nginx.conf"</div>
            <div class="info-item">日志路径: './logs/app.log'</div>
            <div class="info-item">相对路径: "../config/database.yml"</div>
            <div class="info-item">不完整路径: 'assets/css/style.css'</div>
        </div>
        
        <div class="section">
            <h3>🔧 操作按钮</h3>
            <button onclick="loadExternalJS()">加载外部JS</button>
            <button onclick="loadLazyJS()">懒加载JS</button>
            <button onclick="generateDynamicContent()">生成动态内容</button>
        </div>
        
        <div id="dynamic-content"></div>
    </div>

    <!-- 内联JavaScript包含敏感信息 -->
    <script>
        // 配置信息
        const config = {
            apiUrl: "https://api.internal.com/v2",
            dbHost: "***************",
            dbPort: "5432",
            adminEmail: "<EMAIL>",
            secretKey: "secret_key_abcd1234",
            jwtSecret: "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJhdXRoLnNlcnZpY2UiLCJzdWIiOiJ1c2VyMTIzIiwiYXVkIjoiYXBpLnNlcnZpY2UifQ.example",
            paths: {
                logDir: "/var/log/app",
                configFile: "./config/app.json",
                backupPath: "../backup/data"
            }
        };
        
        // 用户信息
        const userInfo = {
            phone: "13900138000",
            email: "<EMAIL>",
            idCard: "******************"
        };
        
        // 算法相关
        function encryptData(data) {
            return CryptoJS.AES.encrypt(data, config.secretKey);
        }
        
        function hashPassword(password) {
            return md5(password + "salt");
        }
        
        function base64Encode(str) {
            return btoa(str);
        }
        
        // 加载外部JavaScript
        function loadExternalJS() {
            const script = document.createElement('script');
            script.src = 'external.js';
            document.head.appendChild(script);
        }
        
        // 懒加载JavaScript
        function loadLazyJS() {
            setTimeout(() => {
                const script = document.createElement('script');
                script.src = 'lazy.js';
                document.head.appendChild(script);
            }, 1000);
        }
        
        // 生成动态内容
        function generateDynamicContent() {
            const dynamicData = {
                apiEndpoint: "https://dynamic.api.com/data",
                serverIP: "************:8080",
                adminContact: "<EMAIL>",
                tempPath: "/tmp/dynamic_data",
                sessionToken: "session_" + Math.random().toString(36).substr(2, 9)
            };
            
            document.getElementById('dynamic-content').innerHTML = `
                <div class="section">
                    <h3>🔄 动态生成的敏感信息</h3>
                    <div class="info-item">动态API: "${dynamicData.apiEndpoint}"</div>
                    <div class="info-item">服务器: '${dynamicData.serverIP}'</div>
                    <div class="info-item">联系人: "${dynamicData.adminContact}"</div>
                    <div class="info-item">临时路径: '${dynamicData.tempPath}'</div>
                    <div class="info-item">会话令牌: "${dynamicData.sessionToken}"</div>
                </div>
            `;
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，配置信息:', config);
            console.log('用户信息:', userInfo);
        });
    </script>
    
    <!-- 外部JavaScript文件引用 -->
    <script src="main.js"></script>
</body>
</html>
