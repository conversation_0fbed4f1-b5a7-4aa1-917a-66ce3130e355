// main.js - 主要的外部JavaScript文件
// 包含各种敏感信息用于测试

// 应用配置
const appConfig = {
    version: "1.0.0",
    environment: "production",
    database: {
        host: "db.example.com",
        port: "3306",
        username: "app_user",
        password: "db_password_123",
        name: "app_database"
    },
    redis: {
        host: "**************",
        port: "6379",
        password: "redis_secret_456"
    },
    api: {
        baseUrl: "https://api.production.com",
        version: "v1",
        endpoints: {
            users: "/api/v1/users",
            orders: "/api/v1/orders",
            payments: "/api/v1/payments"
        }
    },
    security: {
        jwtSecret: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhcHAiLCJuYW1lIjoiQXBwbGljYXRpb24iLCJpYXQiOjE2MzQ1Njc4OTB9.example_signature",
        apiKey: "api_key_prod_789xyz",
        encryptionKey: "encrypt_key_abc123def456"
    },
    logging: {
        level: "info",
        file: "/var/log/app/application.log",
        errorFile: "./logs/error.log",
        accessFile: "../logs/access.log"
    }
};

// 第三方服务配置
const thirdPartyServices = {
    payment: {
        gateway: "https://payment.gateway.com",
        merchantId: "merchant_12345",
        secretKey: "payment_secret_key_xyz789"
    },
    email: {
        smtp: "smtp.mailservice.com",
        port: "587",
        username: "<EMAIL>",
        password: "email_password_456"
    },
    sms: {
        provider: "https://sms.provider.net",
        apiKey: "sms_api_key_789abc",
        signature: "【公司名称】"
    },
    storage: {
        endpoint: "https://storage.cloud.com",
        bucket: "app-storage-bucket",
        accessKey: "storage_access_key_123",
        secretKey: "storage_secret_key_456"
    }
};

// 用户数据示例
const userData = {
    admin: {
        id: 1,
        username: "admin",
        email: "<EMAIL>",
        phone: "13800138001",
        idCard: "110101199001011111"
    },
    testUser: {
        id: 2,
        username: "testuser",
        email: "<EMAIL>",
        phone: "15912345678",
        idCard: "320102198505051234"
    }
};

// 加密和安全相关函数
class SecurityUtils {
    static encrypt(data) {
        // 使用AES加密
        return CryptoJS.AES.encrypt(JSON.stringify(data), appConfig.security.encryptionKey).toString();
    }
    
    static decrypt(encryptedData) {
        const bytes = CryptoJS.AES.decrypt(encryptedData, appConfig.security.encryptionKey);
        return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    }
    
    static generateHash(input) {
        return sha256(input);
    }
    
    static base64Encode(str) {
        return Base64.encode(str);
    }
    
    static base64Decode(str) {
        return Base64.decode(str);
    }
    
    static generateJWT(payload) {
        return KJUR.jws.JWS.sign("HS256", JSON.stringify({typ: "JWT", alg: "HS256"}), JSON.stringify(payload), appConfig.security.jwtSecret);
    }
}

// API调用相关
class ApiClient {
    constructor() {
        this.baseUrl = appConfig.api.baseUrl;
        this.apiKey = appConfig.security.apiKey;
    }
    
    async request(endpoint, options = {}) {
        const url = this.baseUrl + endpoint;
        const headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`,
            'X-API-Key': this.apiKey,
            ...options.headers
        };
        
        return fetch(url, {
            ...options,
            headers
        });
    }
    
    async getUsers() {
        return this.request(appConfig.api.endpoints.users);
    }
    
    async getOrders() {
        return this.request(appConfig.api.endpoints.orders);
    }
}

// 数据库连接相关
class DatabaseConnection {
    constructor() {
        this.config = appConfig.database;
        this.connectionString = `mysql://${this.config.username}:${this.config.password}@${this.config.host}:${this.config.port}/${this.config.name}`;
    }
    
    connect() {
        console.log(`连接数据库: ${this.config.host}:${this.config.port}`);
        // 模拟数据库连接
    }
    
    getConnectionInfo() {
        return {
            host: this.config.host,
            port: this.config.port,
            database: this.config.name,
            user: this.config.username
        };
    }
}

// 日志记录相关
class Logger {
    constructor() {
        this.logFile = appConfig.logging.file;
        this.errorFile = appConfig.logging.errorFile;
    }
    
    log(message) {
        console.log(`[${new Date().toISOString()}] ${message}`);
        // 写入日志文件: this.logFile
    }
    
    error(message) {
        console.error(`[${new Date().toISOString()}] ERROR: ${message}`);
        // 写入错误日志: this.errorFile
    }
}

// 文件路径相关
const filePaths = {
    config: "./config/app.json",
    secrets: "/etc/app/secrets.yml",
    certificates: "../ssl/certificates",
    uploads: "/var/www/uploads",
    temp: "/tmp/app_temp",
    backup: "../backup/daily",
    logs: "/var/log/app",
    static: "assets/static",
    templates: "views/templates"
};

// 网络地址相关
const networkConfig = {
    servers: {
        web: "192.168.1.10:80",
        api: "192.168.1.20:8080",
        database: "192.168.1.30:3306",
        cache: "192.168.1.40:6379",
        queue: "192.168.1.50:5672"
    },
    external: {
        cdn: "https://cdn.example.com",
        analytics: "https://analytics.service.com",
        monitoring: "https://monitor.company.net"
    },
    internal: {
        admin: "http://admin.internal.com",
        dashboard: "https://dashboard.internal.org"
    }
};

// 初始化应用
function initializeApp() {
    console.log('初始化应用配置...');
    console.log('数据库配置:', appConfig.database);
    console.log('API配置:', appConfig.api);
    console.log('安全配置已加载');
    
    // 创建实例
    const apiClient = new ApiClient();
    const dbConnection = new DatabaseConnection();
    const logger = new Logger();
    
    // 连接数据库
    dbConnection.connect();
    
    // 记录启动日志
    logger.log('应用启动成功');
    
    return {
        apiClient,
        dbConnection,
        logger
    };
}

// 导出配置（如果在Node.js环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        appConfig,
        thirdPartyServices,
        userData,
        SecurityUtils,
        ApiClient,
        DatabaseConnection,
        Logger,
        filePaths,
        networkConfig,
        initializeApp
    };
}

// 页面加载时初始化
if (typeof document !== 'undefined') {
    document.addEventListener('DOMContentLoaded', function() {
        const app = initializeApp();
        console.log('主应用已初始化:', app);
    });
}
