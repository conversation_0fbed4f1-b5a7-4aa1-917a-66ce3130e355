// lazy.js - 懒加载的JavaScript文件
// 模拟延迟加载的脚本，包含更多敏感信息

console.log('懒加载JavaScript文件已加载');

// 懒加载配置
const lazyConfig = {
    microservices: {
        userService: {
            url: "https://user.microservice.com",
            port: "8001",
            database: "*************:5432",
            apiKey: "user_service_key_123abc"
        },
        orderService: {
            url: "https://order.microservice.org",
            port: "8002", 
            database: "*************:5432",
            apiKey: "order_service_key_456def"
        },
        paymentService: {
            url: "https://payment.microservice.net",
            port: "8003",
            database: "*************:5432",
            apiKey: "payment_service_key_789ghi"
        }
    },
    monitoring: {
        prometheus: "*************:9090",
        grafana: "*************:3000",
        elasticsearch: "*************:9200",
        kibana: "*************:5601"
    },
    messageQueue: {
        rabbitmq: "*************:5672",
        kafka: "*************:9092",
        redis: "192.168.30.12:6379"
    }
};

// 懒加载用户数据
const lazyUsers = {
    systemAdmin: {
        username: "sysadmin",
        email: "<EMAIL>",
        phone: "13600136003",
        idCard: "******************",
        apiKey: "sysadmin_api_key_xyz789"
    },
    dbAdmin: {
        username: "dbadmin", 
        email: "<EMAIL>",
        phone: "15600156003",
        idCard: "320102198804041111",
        secretKey: "dbadmin_secret_abc123"
    },
    devOps: {
        username: "devops",
        email: "<EMAIL>",
        phone: "18600186003", 
        idCard: "110101199505051234",
        accessToken: "devops_token_def456"
    }
};

// 懒加载路径配置
const lazyPaths = {
    system: {
        config: "/etc/system/config",
        logs: "/var/log/system",
        temp: "/tmp/system",
        backup: "../backup/system"
    },
    application: {
        root: "/opt/application",
        config: "./config/lazy",
        data: "../data/lazy",
        cache: "/var/cache/lazy"
    },
    docker: {
        volumes: "/var/lib/docker/volumes",
        containers: "./docker/containers",
        images: "../docker/images"
    }
};

// 懒加载JWT令牌
const lazyTokens = {
    serviceToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzZXJ2aWNlIjoibGF6eS1zZXJ2aWNlIiwicm9sZSI6ImFkbWluIiwiaWF0IjoxNjM0NTY3ODkwfQ.lazy_service_signature",
    userToken: "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyIjoibGF6eS11c2VyIiwic2NvcGUiOiJyZWFkLXdyaXRlIiwiZXhwIjoxNjM0NTY3ODkwfQ.lazy_user_signature",
    adminToken: "eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZG1pbiI6InRydWUiLCJwZXJtaXNzaW9ucyI6WyJhbGwiXSwiZXhwIjoxNjM0NTY3ODkwfQ.lazy_admin_signature"
};

// 懒加载密钥配置
const lazySecrets = {
    encryption: {
        aesKey: "lazy_aes_key_256bit_abcdef123456",
        rsaPrivateKey: "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----",
        hmacSecret: "lazy_hmac_secret_sha256_789xyz"
    },
    database: {
        masterPassword: "lazy_db_master_pass_123",
        replicationKey: "lazy_replication_key_456",
        backupPassword: "lazy_backup_pass_789"
    },
    api: {
        internalKey: "lazy_internal_api_key_abc",
        externalKey: "lazy_external_api_key_def", 
        webhookSecret: "lazy_webhook_secret_ghi"
    }
};

// 懒加载网络配置
const lazyNetworks = {
    production: {
        loadBalancer: "192.168.100.10:80",
        webServers: ["192.168.100.11:8080", "192.168.100.12:8080"],
        database: "192.168.100.20:3306",
        cache: "192.168.100.21:6379"
    },
    staging: {
        server: "192.168.101.10:8080",
        database: "192.168.101.20:3306",
        cache: "192.168.101.21:6379"
    },
    development: {
        server: "192.168.102.10:3000",
        database: "192.168.102.20:3306",
        cache: "192.168.102.21:6379"
    }
};

// 懒加载域名配置
const lazyDomains = {
    production: {
        main: "app.production.com",
        api: "api.production.com",
        cdn: "cdn.production.com",
        admin: "admin.production.com"
    },
    staging: {
        main: "app.staging.org",
        api: "api.staging.org", 
        admin: "admin.staging.org"
    },
    internal: {
        monitoring: "monitor.internal.net",
        logging: "logs.internal.net",
        backup: "backup.internal.net"
    }
};

// 懒加载算法实现
class LazyAlgorithms {
    static encryptSensitiveData(data) {
        // 使用多种加密算法
        const aes = CryptoJS.AES.encrypt(data, lazySecrets.encryption.aesKey);
        const des = CryptoJS.DES.encrypt(data, "lazy_des_key");
        const base64 = btoa(data);
        
        return { aes, des, base64 };
    }
    
    static hashData(data) {
        const md5Hash = md5(data);
        const sha1Hash = sha1(data);
        const sha256Hash = sha256(data);
        
        return { md5Hash, sha1Hash, sha256Hash };
    }
    
    static generateJWT(payload) {
        return KJUR.jws.JWS.sign(
            "HS256",
            JSON.stringify({typ: "JWT", alg: "HS256"}),
            JSON.stringify(payload),
            lazySecrets.encryption.hmacSecret
        );
    }
    
    static rsaEncrypt(data) {
        const encrypt = new JSEncrypt();
        encrypt.setPublicKey(lazySecrets.encryption.rsaPrivateKey);
        return encrypt.encrypt(data);
    }
}

// 懒加载联系信息
const lazyContacts = {
    emergency: {
        email: "<EMAIL>",
        phone: "***********",
        backup: "<EMAIL>"
    },
    operations: {
        email: "<EMAIL>", 
        phone: "***********",
        oncall: "<EMAIL>"
    },
    security: {
        email: "<EMAIL>",
        phone: "***********",
        incident: "<EMAIL>"
    }
};

// 懒加载身份信息
const lazyIdentities = {
    serviceAccount: "440106199404041234",
    backupAccount: "320102198907071111", 
    monitorAccount: "110101199608081234"
};

// 懒加载初始化函数
function initializeLazy() {
    console.log('懒加载模块初始化...');
    console.log('微服务配置:', lazyConfig.microservices);
    console.log('监控配置:', lazyConfig.monitoring);
    console.log('消息队列配置:', lazyConfig.messageQueue);
    
    // 执行算法测试
    const testData = "lazy test data";
    const encrypted = LazyAlgorithms.encryptSensitiveData(testData);
    const hashed = LazyAlgorithms.hashData(testData);
    const jwt = LazyAlgorithms.generateJWT({user: "lazy", role: "test"});
    
    console.log('懒加载算法测试完成:', { encrypted, hashed, jwt });
    
    // 显示懒加载成功消息
    if (typeof document !== 'undefined') {
        const message = document.createElement('div');
        message.className = 'section';
        message.innerHTML = `
            <h3>⏰ 懒加载JavaScript已加载</h3>
            <div class="info-item">用户服务: "${lazyConfig.microservices.userService.url}"</div>
            <div class="info-item">订单服务: "${lazyConfig.microservices.orderService.url}"</div>
            <div class="info-item">支付服务: "${lazyConfig.microservices.paymentService.url}"</div>
            <div class="info-item">监控地址: "${lazyConfig.monitoring.prometheus}"</div>
            <div class="info-item">系统管理员: "${lazyUsers.systemAdmin.email}"</div>
            <div class="info-item">系统管理员手机: "${lazyUsers.systemAdmin.phone}"</div>
            <div class="info-item">系统管理员身份证: "${lazyUsers.systemAdmin.idCard}"</div>
            <div class="info-item">生产域名: "${lazyDomains.production.main}"</div>
            <div class="info-item">API域名: "${lazyDomains.production.api}"</div>
        `;
        
        const container = document.querySelector('.container');
        if (container) {
            container.appendChild(message);
        }
    }
}

// 立即执行懒加载初始化
initializeLazy();
