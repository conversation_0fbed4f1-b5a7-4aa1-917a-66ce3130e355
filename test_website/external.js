// external.js - 外部加载的JavaScript文件
// 模拟通过按钮动态加载的外部脚本

console.log('外部JavaScript文件已加载');

// 外部服务配置
const externalConfig = {
    services: {
        auth: {
            url: "https://auth.external.com",
            clientId: "client_id_external_123",
            clientSecret: "client_secret_external_456",
            redirectUri: "https://app.com/callback"
        },
        analytics: {
            url: "https://analytics.external.org",
            trackingId: "GA-12345-67",
            apiKey: "analytics_key_789xyz"
        },
        payment: {
            gateway: "https://pay.external.net",
            merchantKey: "merchant_key_abc123",
            webhookSecret: "webhook_secret_def456"
        }
    },
    database: {
        external: {
            host: "external-db.company.com",
            port: "5432",
            username: "external_user",
            password: "external_pass_123"
        }
    },
    cache: {
        redis: "***************:6379",
        memcached: "***************:11211"
    }
};

// 外部用户数据
const externalUsers = {
    partner: {
        email: "<EMAIL>",
        phone: "13700137001",
        api<PERSON>ey: "partner_api_key_xyz789"
    },
    vendor: {
        email: "<EMAIL>",
        phone: "15800158001",
        secretKey: "vendor_secret_abc123"
    }
};

// 外部API路径
const externalPaths = {
    api: {
        auth: "/oauth/token",
        users: "/api/external/users",
        data: "/api/external/data"
    },
    files: {
        uploads: "/external/uploads",
        downloads: "../external/downloads",
        temp: "./external/temp"
    }
};

// 外部加密工具
class ExternalCrypto {
    static encryptWithRSA(data) {
        // 模拟RSA加密
        const encrypted = rsa.encrypt(data, externalConfig.services.auth.clientSecret);
        return encrypted;
    }
    
    static generateMD5(input) {
        return $().md5(input);
    }
    
    static sha1Hash(data) {
        return sha1(data);
    }
}

// 外部网络地址
const externalNetworks = {
    servers: {
        proxy: "192.168.100.1:8080",
        loadBalancer: "192.168.100.2:80",
        backup: "192.168.100.3:22"
    },
    domains: {
        api: "api.external.com",
        cdn: "cdn.external.net",
        static: "static.external.org"
    },
    urls: {
        webhook: "https://webhook.external.com/notify",
        callback: "https://callback.external.org/process",
        status: "https://status.external.net/health"
    }
};

// 外部JWT令牌示例
const externalTokens = {
    accessToken: "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJleHRlcm5hbC1hdXRoIiwic3ViIjoiZXh0ZXJuYWwtdXNlciIsImF1ZCI6ImV4dGVybmFsLWFwaSJ9.external_signature",
    refreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************.refresh_signature",
    apiToken: "bearer_token_external_abc123def456"
};

// 外部文件路径
const externalFilePaths = {
    config: "/etc/external/config.json",
    logs: "/var/log/external/app.log",
    cache: "./cache/external",
    temp: "../temp/external",
    backup: "/backup/external/daily",
    uploads: "uploads/external",
    downloads: "downloads/external"
};

// 外部联系信息
const externalContacts = {
    support: {
        email: "<EMAIL>",
        phone: "***********"
    },
    technical: {
        email: "<EMAIL>",
        phone: "***********"
    },
    business: {
        email: "<EMAIL>",
        phone: "***********"
    }
};

// 外部身份证示例
const externalIdentities = {
    testId1: "440106199101011111",
    testId2: "320102198606061234",
    testId3: "110101199212121111"
};

// 外部算法使用
function externalAlgorithms() {
    // Base64编码
    const encoded = Base64.encode("external data");
    
    // AES加密
    const aesEncrypted = CryptoJS.AES.encrypt("sensitive data", "external_key");
    
    // DES加密
    const desEncrypted = CryptoJS.DES.encrypt("secret info", "des_key");
    
    // JSEncrypt使用
    const encrypt = new JSEncrypt();
    encrypt.setPublicKey("external_public_key");
    const encrypted = encrypt.encrypt("data to encrypt");
    
    return {
        encoded,
        aesEncrypted,
        desEncrypted,
        encrypted
    };
}

// 外部初始化函数
function initializeExternal() {
    console.log('外部模块初始化...');
    console.log('外部配置:', externalConfig);
    console.log('外部用户:', externalUsers);
    console.log('外部网络:', externalNetworks);
    
    // 执行外部算法
    const algorithms = externalAlgorithms();
    console.log('外部算法执行完成:', algorithms);
    
    // 显示加载成功消息
    if (typeof document !== 'undefined') {
        const message = document.createElement('div');
        message.className = 'section';
        message.innerHTML = `
            <h3>🔗 外部JavaScript已加载</h3>
            <div class="info-item">外部API: "${externalConfig.services.auth.url}"</div>
            <div class="info-item">外部数据库: "${externalConfig.database.external.host}"</div>
            <div class="info-item">外部缓存: "${externalConfig.cache.redis}"</div>
            <div class="info-item">外部联系: "${externalContacts.support.email}"</div>
            <div class="info-item">外部手机: "${externalContacts.support.phone}"</div>
        `;
        
        const container = document.querySelector('.container');
        if (container) {
            container.appendChild(message);
        }
    }
}

// 立即执行初始化
initializeExternal();
