#!/bin/bash

# 测试FindSomething表结构的脚本
# 验证新的findsomething表是否正确创建，并包含13种类型的数据

echo "🔍 测试FindSomething表结构..."

# 设置Go环境
export GOROOT=/opt/homebrew/Cellar/go/1.24.5/libexec
export PATH=$GOROOT/bin:$PATH

# 进入项目目录
cd "/Users/<USER>/Documents/goland projects/webscan"

# 测试URL
TEST_URL="https://example.com"

echo "📋 测试步骤："
echo "1. 重置数据库表结构"
echo "2. 扫描测试URL: $TEST_URL"
echo "3. 验证findsomething表中的13种类型数据"
echo ""

# 1. 重置数据库
echo "🗑️  重置数据库表结构..."
./webscan -reset-db -dbconfig config/database.json
if [ $? -ne 0 ]; then
    echo "❌ 数据库重置失败"
    exit 1
fi
echo "✅ 数据库重置成功"
echo ""

# 2. 执行扫描
echo "🔍 开始扫描 $TEST_URL ..."
./webscan -url "$TEST_URL" -jsinfo -db -dbconfig config/database.json
if [ $? -ne 0 ]; then
    echo "❌ 扫描失败"
    exit 1
fi
echo "✅ 扫描完成"
echo ""

# 3. 验证数据库结果
echo "📊 验证findsomething表结构和数据..."

# 连接MySQL并查询
mysql -h127.0.0.1 -uroot -pLyf123@@ -Dwebscan << 'EOF'
-- 显示表结构
DESCRIBE findsomething;

-- 显示所有数据类型
SELECT DISTINCT info_type FROM findsomething ORDER BY info_type;

-- 显示每种类型的数据统计
SELECT 
    info_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN value != '' THEN 1 END) as non_empty_count,
    COUNT(CASE WHEN value = '' THEN 1 END) as empty_count
FROM findsomething 
GROUP BY info_type 
ORDER BY info_type;

-- 显示具体数据示例（前20条）
SELECT task_id, info_type, 
       CASE 
           WHEN value = '' THEN '[空值]'
           WHEN LENGTH(value) > 50 THEN CONCAT(LEFT(value, 50), '...')
           ELSE value
       END as display_value,
       source
FROM findsomething 
ORDER BY info_type, id 
LIMIT 20;
EOF

echo ""
echo "🎯 验证要点："
echo "1. findsomething表应该包含13种info_type："
echo "   - ip, ip_port, domain, path, incomplete_path, url"
echo "   - sfz, mobile, mail, jwt, algorithm, secret, static"
echo "2. 每种类型都应该有至少一条记录（即使是空值）"
echo "3. value字段允许为空（NULL或空字符串）"
echo "4. source字段记录信息来源"
echo ""
echo "✅ 测试完成！"
