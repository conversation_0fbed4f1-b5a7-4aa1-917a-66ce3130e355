# FindSomething移植快速入门指南

## 🎯 5分钟了解移植过程

### 什么是移植？
简单来说，就是把一个项目的功能搬到另一个项目里。
- **原项目**: FindSomething（浏览器扩展，用JavaScript写的）
- **目标项目**: WebScan（网络爬虫，用Go语言写的）
- **移植目标**: 把FindSomething的敏感信息检测功能搬到WebScan里

### 为什么要移植？
1. **功能强大**: FindSomething能检测11种敏感信息（身份证、手机号、API密钥等）
2. **规则完善**: 包含710条专业的密钥检测规则
3. **准确性高**: 经过大量实际使用验证

## 🔧 移植的核心挑战

### 挑战1: 语言不同
- **FindSomething**: 用JavaScript写的
- **WebScan**: 用Go语言写的
- **解决方案**: 在Go里嵌入一个JavaScript引擎

### 挑战2: 运行环境不同
- **FindSomething**: 在浏览器里运行
- **WebScan**: 在服务器上运行
- **解决方案**: 重新设计数据流程

## 🛠️ 移植方案选择

### 方案A: 重写代码（❌不推荐）
```
FindSomething的JavaScript代码 → 手动翻译 → Go代码
```
**问题**: 容易出错，难以保证100%兼容

### 方案B: 嵌入JavaScript引擎（✅推荐）
```
FindSomething的JavaScript代码 → JavaScript引擎 → Go程序调用
```
**优点**: 100%兼容，不会出错

## 📋 移植步骤详解

### 第1步: 准备工作
```bash
# 1. 下载FindSomething项目
git clone https://github.com/ResidualLaugh/FindSomething.git

# 2. 安装JavaScript引擎
go get github.com/dop251/goja
```

### 第2步: 复制核心代码
找到FindSomething项目中的关键文件：
- `background.js` - 包含检测逻辑
- 复制其中的`extract_info`函数和`nuclei_regex`数组

### 第3步: 创建Go包装器
```go
// 创建一个Go结构体来包装JavaScript功能
type GojaExtractor struct {
    vm *goja.Runtime  // JavaScript引擎
    // 其他字段...
}

// 初始化JavaScript环境
func (ge *GojaExtractor) init() {
    ge.vm = goja.New()  // 创建JavaScript引擎
    ge.vm.RunString(findSomethingJSCode)  // 加载JavaScript代码
}
```

### 第4步: 数据转换
```go
// JavaScript返回的数据 → Go语言的数据结构
func convertJSToGo(jsResult) *SensitiveInfo {
    // 转换逻辑...
}
```

### 第5步: 数据库存储
```go
// 把检测结果存储到数据库
func saveToDatabase(result *SensitiveInfo) {
    // 存储逻辑...
}
```

## 🎮 实际使用示例

### 简单使用
```go
// 1. 创建检测器
extractor := NewGojaExtractor()

// 2. 准备要检测的JavaScript代码
jsCode := `var config = {"api_key": "sk-123", "email": "<EMAIL>"};`

// 3. 执行检测
result := extractor.ExtractSensitiveInfo(jsCode, "config.js")

// 4. 查看结果
fmt.Printf("找到邮箱: %s\n", result.Mail[0].Value)
```

### 完整流程
```go
func main() {
    // 1. 初始化
    extractor := GetSensitiveInfoExtractor()
    
    // 2. 扫描网页
    webContent := downloadWebPage("https://example.com")
    
    // 3. 提取敏感信息
    sensitiveInfo := extractor.ExtractSensitiveInfo(webContent, "https://example.com")
    
    // 4. 存储到数据库
    saveToDatabase(sensitiveInfo)
    
    // 5. 输出结果
    printResults(sensitiveInfo)
}
```

## 🔍 能检测什么？

### 个人信息
- 🆔 身份证号: `110101199001011234`
- 📱 手机号: `13812345678`
- 📧 邮箱: `<EMAIL>`

### 技术信息
- 🌐 IP地址: `***********`
- 🔗 域名: `api.example.com`
- 📂 路径: `/api/v1/users`

### 安全信息
- 🔑 API密钥: `sk-1234567890abcdef`
- 🎫 JWT令牌: `eyJhbGciOiJIUzI1NiIs...`
- 🔐 算法调用: `md5()`, `Base64.encode()`

## 📊 移植前后对比

| 特性 | FindSomething | WebScan+FindSomething |
|------|---------------|----------------------|
| 运行环境 | 浏览器 | 服务器 |
| 数据存储 | 临时显示 | 数据库持久化 |
| 处理能力 | 单页面 | 批量处理 |
| 使用方式 | 手动点击 | 自动化扫描 |

## 🚀 移植的好处

### 对用户的好处
1. **自动化**: 不需要手动操作浏览器
2. **批量处理**: 可以同时扫描多个网站
3. **数据持久化**: 结果保存在数据库中
4. **集成能力**: 可以集成到其他安全工具中

### 对开发者的好处
1. **代码复用**: 直接使用成熟的检测规则
2. **维护简单**: 规则更新只需要同步JavaScript代码
3. **扩展性强**: 可以轻松添加新的检测类型

## 🎯 关键技术点

### 1. JavaScript引擎选择
**为什么选择goja？**
- 纯Go实现，不需要外部依赖
- 性能好，内存占用合理
- 支持ES5标准，满足需求

### 2. 并发安全
**问题**: JavaScript引擎不是线程安全的
**解决**: 使用互斥锁保护
```go
var mu sync.Mutex
mu.Lock()
result := jsEngine.Call()
mu.Unlock()
```

### 3. 性能优化
**问题**: 每次都创建JavaScript引擎很慢
**解决**: 使用单例模式，只创建一次
```go
var globalExtractor *GojaExtractor
var once sync.Once

func GetExtractor() *GojaExtractor {
    once.Do(func() {
        globalExtractor = NewGojaExtractor()
    })
    return globalExtractor
}
```

## 🔧 调试技巧

### 1. 检查JavaScript是否正确加载
```go
if extractor == nil {
    log.Fatal("JavaScript引擎初始化失败")
}
```

### 2. 验证检测结果
```go
// 用已知的测试数据验证
testData := `"email": "<EMAIL>"`
result := extractor.ExtractSensitiveInfo(testData, "test")
if len(result.Mail) == 0 {
    log.Error("邮箱检测失败")
}
```

### 3. 性能监控
```go
start := time.Now()
result := extractor.ExtractSensitiveInfo(data, source)
fmt.Printf("检测耗时: %v\n", time.Since(start))
```

## 📝 总结

通过这次移植，我们成功地：
1. **保持了100%兼容性** - 直接使用原版JavaScript代码
2. **提升了实用性** - 从浏览器扩展变成了服务器工具
3. **增强了功能** - 添加了数据库存储和批量处理
4. **保证了性能** - 通过优化设计确保高效运行

这是一个成功的跨语言功能移植案例，展示了如何在保持原有功能完整性的同时，适应新的技术环境和使用场景。

## 🎓 学习要点

如果你想做类似的移植工作，记住这几个关键点：
1. **理解原理** - 先搞清楚原项目是怎么工作的
2. **选对方案** - 选择最能保证兼容性的技术方案
3. **分步实施** - 把复杂的移植工作分解成小步骤
4. **充分测试** - 确保移植后的功能与原版一致
5. **性能优化** - 针对新环境进行必要的性能优化
