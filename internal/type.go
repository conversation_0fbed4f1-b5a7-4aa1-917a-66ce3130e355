package internal

// FindSomethingResult 完全按照findsomething浏览器插件的数据结构设计
// 对应findsomething中的search_data结构
type FindSomethingResult struct {
	Current        string              `json:"current"`         // 当前URL
	Done           string              `json:"done"`            // 完成状态
	TaskList       []int               `json:"tasklist"`        // 任务列表
	DoneTaskList   []int               `json:"donetasklist"`    // 完成任务列表
	PreTaskNum     int                 `json:"pretasknum"`      // 预期任务数量
	Source         map[string]string   `json:"source"`          // 敏感信息来源映射

	// 敏感信息字段 - 完全对应findsomething的key数组：["ip","ip_port","domain","path","incomplete_path","url","sfz","mobile","mail","jwt","algorithm","secret"]
	IP             []string            `json:"ip,omitempty"`             // IP地址
	IPPort         []string            `json:"ip_port,omitempty"`        // IP:端口
	Domain         []string            `json:"domain,omitempty"`         // 域名
	Path           []string            `json:"path,omitempty"`           // 路径
	IncompletePath []string            `json:"incomplete_path,omitempty"` // 不完整路径
	URL            []string            `json:"url,omitempty"`            // URL
	SFZ            []string            `json:"sfz,omitempty"`            // 身份证
	Mobile         []string            `json:"mobile,omitempty"`         // 手机号
	Mail           []string            `json:"mail,omitempty"`           // 邮箱
	JWT            []string            `json:"jwt,omitempty"`            // JWT令牌
	Algorithm      []string            `json:"algorithm,omitempty"`      // 算法
	Secret         []string            `json:"secret,omitempty"`         // 密钥
}

// SensitiveInfo 为了兼容现有代码，保留这个结构但简化
type SensitiveInfo struct {
	FindSomethingData *FindSomethingResult `json:"findsomething_data,omitempty"`
}

type APIEntry struct {
	URL        string `json:"url"`
	Method     string `json:"method"`
	PostData   string `json:"postData"`
	Status     int    `json:"status"`
	Mime       string `json:"mime"`
	Response   string `json:"response,omitempty"` // debug 模式下记录
	FromScript bool   `json:"fromScript"`         // 是否动态请求
}

type Resource struct {
	URL     string `json:"url"`
	Type    string `json:"type"`
	FromDOM bool   `json:"fromDom"`
	FromJS  bool   `json:"fromJs"`
	Status  int    `json:"status"`
	Mime    string `json:"mime"`
}

type ScanResult struct {
	URL       string     `json:"url"`
	ParentURL string     `json:"parent,omitempty"`
	Timestamp string     `json:"timestamp"`
	Title     string     `json:"title"`
	Status    int        `json:"status"`
	HTMLList  []string   `json:"htmlList"`
	JSList    []string   `json:"jsList"`
	CSSList   []string   `json:"cssList"`
	ImageList []string   `json:"imageList"`
	FontList  []string   `json:"fontList"`     // 新增：字体文件列表
	DocList   []string   `json:"docList"`      // 新增：文档文件列表
	APIList   []APIEntry `json:"apiList"`
	OtherList []string   `json:"otherList"`

	// 敏感信息
	SensitiveInfo *SensitiveInfo `json:"sensitiveInfo,omitempty"`

	Counts struct {
		HTML     int `json:"html"`
		JS       int `json:"js"`
		CSS      int `json:"css"`
		Image    int `json:"image"`
		Font     int `json:"font"`     // 新增：字体文件计数
		Document int `json:"document"` // 新增：文档文件计数
		API      int `json:"api"`
		Other    int `json:"other"`
	} `json:"counts"`
}
