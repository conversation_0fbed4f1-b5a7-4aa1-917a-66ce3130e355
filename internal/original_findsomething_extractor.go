package internal

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"sync"
	"web_scanner/core"

	"github.com/dop251/goja"
)

// OriginalFindSomethingExtractor 使用原版findsomething的background.js进行敏感信息提取
// 直接执行原版JavaScript代码，确保100%兼容性
type OriginalFindSomethingExtractor struct {
	logger       *core.Logger
	errorManager *core.ErrorManager
	
	// JavaScript运行时和相关资源
	vm           *goja.Runtime
	extractFunc  goja.Callable
	mu           sync.Mutex // 保护JavaScript运行时的并发访问
	
	// 初始化状态
	initialized  bool
}

// NewOriginalFindSomethingExtractor 创建新的原版FindSomething敏感信息提取器实例
func NewOriginalFindSomethingExtractor() *OriginalFindSomethingExtractor {
	extractor := &OriginalFindSomethingExtractor{
		logger:       core.GetLogger(),
		errorManager: core.GetErrorManager(),
		initialized:  false,
	}
	
	// 初始化JavaScript运行时
	if err := extractor.initializeJSRuntime(); err != nil {
		extractor.logger.Error("original_findsomething_extractor", "初始化JavaScript运行时失败", map[string]interface{}{
			"error": err.Error(),
		})
		return nil
	}
	
	extractor.initialized = true
	// 初始化成功信息已在主程序中显示
	
	return extractor
}

// initializeJSRuntime 初始化JavaScript运行时环境
func (ofe *OriginalFindSomethingExtractor) initializeJSRuntime() error {
	ofe.mu.Lock()
	defer ofe.mu.Unlock()

	// 创建新的JavaScript运行时
	ofe.vm = goja.New()

	// 直接使用适配的JavaScript代码（包含完整的nuclei规则）
	adaptedJSCode := ofe.adaptOriginalJSCode("")

	// 加载JavaScript代码到运行时
	_, err := ofe.vm.RunString(adaptedJSCode)
	if err != nil {
		return fmt.Errorf("加载原版FindSomething核心代码失败: %w", err)
	}

	// 获取extract_info函数
	extractInfoValue := ofe.vm.Get("extract_info")
	if extractInfoValue == nil {
		return fmt.Errorf("未找到extract_info函数")
	}

	// 转换为可调用函数
	var ok bool
	ofe.extractFunc, ok = goja.AssertFunction(extractInfoValue)
	if !ok {
		return fmt.Errorf("extract_info不是一个有效的函数")
	}

	ofe.logger.Debug("original_findsomething_extractor", "原版background.js运行时初始化完成", nil)
	return nil
}

// adaptOriginalJSCode 适配原版background.js代码以在goja环境中运行
// 读取完整的nuclei规则并构建JavaScript代码
func (ofe *OriginalFindSomethingExtractor) adaptOriginalJSCode(jsCode string) string {
	// 读取完整的nuclei规则
	nucleiRulesContent, err := ioutil.ReadFile("nuclei_rules_extracted.js")
	if err != nil {
		// 静默处理：nuclei规则文件不存在，使用基本规则
		// 如果读取失败，使用基本的规则
		nucleiRulesContent = []byte("var nuclei_regex = [];")
	}

	// 构建完整的JavaScript代码，包含原版的所有核心函数
	return `
// 从原版background.js提取的核心变量和函数
var js = [];
var search_data = {};
var static_file = ['.jpg','.png','.gif','.css','.svg','.ico','.js'];
var non_static_file = ['.jsp'];
var key = ["ip","ip_port","domain","path","incomplete_path","url","sfz","mobile","mail","jwt","algorithm","secret"];
var not_sub_key = ["secret"];

// 完整的nuclei正则表达式数组（从原版background.js提取）
` + string(nucleiRulesContent) + `

// 辅助函数
function unique(arr1){
    if(arr1 == 'null' || arr1 == null){
        return null;
    }
    let arr2=[];
    arr1.forEach(function (item,index,array) {
        if(arr2.indexOf(item)==-1){
            arr2.push(item)
        }
    })
    return arr2;
}

function add(arr1, arr2) {
    if(arr1 == null && arr2 == null){
        return null;
    }
    if(arr1 == null){
        return arr2;
    }
    if(arr2 == null){
        return arr1;
    }
    var arr3 = [];
    arr1.forEach(function (item,index,array) {
        if(arr3.indexOf(item)==-1){
            arr3.push(item)
        }
    })
    arr2.forEach(function (item,index,array) {
        if(arr3.indexOf(item)==-1){
            arr3.push(item)
        }
    })
    return arr3;
}

function sub_1(arr1) {
    var arr3 = []
    arr1.forEach(function (item,index,array) {
        let start = 0
        let end = 0
        if(item.startsWith("'") || item.startsWith('"')){
            start = 1
        }
        if(item.endsWith("'") || item.endsWith('"')){
            end = 1
        }
        arr3.push(item.substring(start,item.length-end))
    })
    return arr3
}

function get_secret(data) {
    var result = [];
    for (var i = nuclei_regex.length - 1; i >= 0; i--) {
        var tmp_result = data.match(nuclei_regex[i]);
        if (tmp_result != null){
            for(var j in tmp_result){
                result.push(tmp_result[j]);
            }
        }
    }
    return result;
}

// 原版数据处理函数
function persist_tmp_data(tmp_data, req_url, current) {
    // 初始化search_data结构
    if (!(current in search_data)) {
        search_data[current] = {
            'current': current,
            'tasklist': [],
            'donetasklist': [],
            'source': {}
        };
    }

    // 遍历所有数据类型
    for (var i = 0; i < key.length; i++) {
        // 如果传入的数据没有这个类型，就看下一个
        if (tmp_data[key[i]] == null){
          continue;
        }

        // 把前端的处理放到这里避免重复
        if (not_sub_key.indexOf(key[i]) < 0){
          tmp_data[key[i]] = sub_1(tmp_data[key[i]])
        }

        tmp_data[key[i]].map((item)=>{
            search_data[current]['source'][item] = req_url
        })

        if (current in search_data && search_data[current][key[i]] != null ){
          var search_data_value = unique(add(search_data[current][key[i]], tmp_data[key[i]])).sort()
          search_data[current][key[i]] = search_data_value
        } else {
          var search_data_value = unique(tmp_data[key[i]]).sort()
          search_data[current][key[i]] = search_data_value
        }
    }
}

// 核心的extract_info函数（从原版复制并优化）
function extract_info(data, source_url) {
    var extract_data = {}
    extract_data['sfz'] = data.match(/['"]((\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(\d{6}(18|19|20)\d{2}(0[1-9]|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)))['"]/g);
    extract_data['mobile'] = data.match(/['"](1(3([0-35-9]\d|4[1-8])|4[14-9]\d|5([\d]\d|7[1-79])|66\d|7[2-35-8]\d|8\d{2}|9[89]\d)\d{7})['"]/g);
    extract_data['mail'] = data.match(/['"][a-zA-Z0-9\._\-]*@[a-zA-Z0-9\._\-]{1,63}\.((?!js|css|jpg|jpeg|png|ico)[a-zA-Z]{2,})['"]/g);
    extract_data['ip'] = data.match(/['"](([a-zA-Z0-9]+:)?\/\/)?\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(\/.*?)?['"]/g);
    extract_data['ip_port'] = data.match(/['"](([a-zA-Z0-9]+:)?\/\/)?\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\:\d{1,5}(\/.*?)?['"]/g);
    extract_data['domain'] = data.match(/['"](([a-zA-Z0-9]+:)?\/\/)?[a-zA-Z0-9\-\.]*?\.(xin|com|cn|net|com.cn|vip|top|cc|shop|club|wang|xyz|luxe|site|news|pub|fun|online|win|red|loan|ren|mom|net.cn|org|link|biz|bid|help|tech|date|mobi|so|me|tv|co|vc|pw|video|party|pics|website|store|ltd|ink|trade|live|wiki|space|gift|lol|work|band|info|click|photo|market|tel|social|press|game|kim|org.cn|games|pro|men|love|studio|rocks|asia|group|science|design|software|engineer|lawyer|fit|beer|tw|我爱你|中国|公司|网络|在线|网址|网店|集团|中文网)(\:\d{1,5})?(\/)?['"]/g);
    extract_data['path'] = data.match(/['"](?:\/|\.\.\/|\.\/)[^\/\>\< \)\(\{\}\,\'\"\\]([^\>\< \)\(\{\}\,\'\"\\])*?['"]/g);
    extract_data['incomplete_path'] = data.match(/['"][^\/\>\< \)\(\{\}\,\'\"\\][\w\/]*?\/[\w\/]*?['"]/g);
    extract_data['url'] = data.match(/['"](([a-zA-Z0-9]+:)?\/\/)?[a-zA-Z0-9\-\.]*?\.(xin|com|cn|net|com.cn|vip|top|cc|shop|club|wang|xyz|luxe|site|news|pub|fun|online|win|red|loan|ren|mom|net.cn|org|link|biz|bid|help|tech|date|mobi|so|me|tv|co|vc|pw|video|party|pics|website|store|ltd|ink|trade|live|wiki|space|gift|lol|work|band|info|click|photo|market|tel|social|press|game|kim|org.cn|games|pro|men|love|studio|rocks|asia|group|science|design|software|engineer|lawyer|fit|beer|tw|我爱你|中国|公司|网络|在线|网址|网店|集团|中文网)(\:\d{1,5})?(\/.*?)?['"]/g);
    extract_data['jwt'] = data.match(/['"](ey[A-Za-z0-9_-]{10,}\.[A-Za-z0-9._-]{10,}|ey[A-Za-z0-9_\/+-]{10,}\.[A-Za-z0-9._\/+-]{10,})['"]/g);
    extract_data['algorithm'] = data.match(/\W(Base64\.encode|Base64\.decode|btoa|atob|CryptoJS\.AES|CryptoJS\.DES|JSEncrypt|rsa|KJUR|\$\.md5|md5|sha1|sha256|sha512)[\(\.]/gi);
    extract_data['secret'] = get_secret(data);

    if (extract_data['url']){
        extract_data['url'].map((url)=>{
            extract_data['ip'] = add(extract_data['ip'], url.match(/['"](([a-zA-Z0-9]+:)?\/\/)?\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/g))
            extract_data['ip_port'] = add(extract_data['ip_port'], url.match(/['"](([a-zA-Z0-9]+:)?\/\/)?\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\:\d{1,5}(\/.*?)?['"]/g))
            extract_data['domain'] = add(extract_data['domain'], url.match(/['"](([a-zA-Z0-9]+:)?\/\/)?[a-zA-Z0-9\-\.]*?\.(xin|com|cn|net|com.cn|vip|top|cc|shop|club|wang|xyz|luxe|site|news|pub|fun|online|win|red|loan|ren|mom|net.cn|org|link|biz|bid|help|tech|date|mobi|so|me|tv|co|vc|pw|video|party|pics|website|store|ltd|ink|trade|live|wiki|space|gift|lol|work|band|info|click|photo|market|tel|social|press|game|kim|org.cn|games|pro|men|love|studio|rocks|asia|group|science|design|software|engineer|lawyer|fit|beer|tw|我爱你|中国|公司|网络|在线|网址|网店|集团|中文网)(\:\d{1,5})?/g))
        })
    }

    // 使用原版的数据处理逻辑
    if (source_url) {
        extract_data['current'] = source_url;
        persist_tmp_data(extract_data, source_url, source_url);

        // 返回处理后的数据
        if (search_data[source_url]) {
            var result = {
                'current': source_url,
                'source': search_data[source_url]['source'] || {}
            };

            // 复制所有敏感信息类型
            for (var i = 0; i < key.length; i++) {
                result[key[i]] = search_data[source_url][key[i]] || [];
            }

            return result;
        }
    }

    return extract_data;
}
`
}

// ExtractSensitiveInfo 从JavaScript代码中提取敏感信息
// 使用原版findsomething的extract_info函数
// 参数：
//   - data: 要分析的JavaScript代码内容
//   - source: 数据来源URL
// 返回值：
//   - *FindSomethingResult: 提取到的敏感信息结果
func (ofe *OriginalFindSomethingExtractor) ExtractSensitiveInfo(data, source string) *FindSomethingResult {
	if !ofe.initialized {
		ofe.logger.Error("original_findsomething_extractor", "提取器未初始化", map[string]interface{}{
			"source": source,
		})
		return nil
	}
	
	if data == "" {
		ofe.logger.Debug("original_findsomething_extractor", "输入数据为空", map[string]interface{}{
			"source": source,
		})
		return nil
	}
	
	// 使用互斥锁保护JavaScript运行时的并发访问
	ofe.mu.Lock()
	defer ofe.mu.Unlock()
	
	// 调用原版JavaScript的extract_info函数，传递source参数
	result, err := ofe.extractFunc(goja.Undefined(), ofe.vm.ToValue(data), ofe.vm.ToValue(source))
	if err != nil {
		ofe.logger.Error("original_findsomething_extractor", "原版JavaScript函数执行失败", map[string]interface{}{
			"source": source,
			"error":  err.Error(),
		})
		return nil
	}
	
	// 将JavaScript结果转换为Go结构体
	findSomethingResult := ofe.convertJSResultToGoStruct(result, source)
	
	if findSomethingResult != nil {
		ofe.logger.Debug("original_findsomething_extractor", "原版敏感信息提取完成", map[string]interface{}{
			"source":        source,
			"ip_count":      len(findSomethingResult.IP),
			"domain_count":  len(findSomethingResult.Domain),
			"mobile_count":  len(findSomethingResult.Mobile),
			"mail_count":    len(findSomethingResult.Mail),
			"secret_count":  len(findSomethingResult.Secret),
			"path_count":    len(findSomethingResult.Path),
		})
	}
	
	return findSomethingResult
}

// convertJSResultToGoStruct 将原版JavaScript结果转换为Go结构体
func (ofe *OriginalFindSomethingExtractor) convertJSResultToGoStruct(jsResult goja.Value, source string) *FindSomethingResult {
	// 将JavaScript对象转换为JSON字符串
	jsonStr, err := json.Marshal(jsResult.Export())
	if err != nil {
		ofe.logger.Error("original_findsomething_extractor", "JavaScript结果序列化失败", map[string]interface{}{
			"source": source,
			"error":  err.Error(),
		})
		return nil
	}
	
	// 解析JavaScript返回的结果
	var jsExtractData map[string]interface{}
	if err := json.Unmarshal(jsonStr, &jsExtractData); err != nil {
		ofe.logger.Error("original_findsomething_extractor", "JavaScript结果解析失败", map[string]interface{}{
			"source": source,
			"error":  err.Error(),
		})
		return nil
	}
	
	// 创建FindSomethingResult结构体
	result := &FindSomethingResult{
		Current: source,
		Source:  make(map[string]string),
	}

	// 处理current字段
	if currentVal, ok := jsExtractData["current"]; ok {
		if currentStr, ok := currentVal.(string); ok {
			result.Current = currentStr
		}
	}

	// 处理source映射
	if sourceVal, ok := jsExtractData["source"]; ok {
		if sourceMap, ok := sourceVal.(map[string]interface{}); ok {
			for key, value := range sourceMap {
				if valueStr, ok := value.(string); ok {
					result.Source[key] = valueStr
				}
			}
		}
	}

	// 转换各种敏感信息类型（按照findsomething的12种类型）
	result.IP = ofe.convertStringArray(jsExtractData["ip"])
	result.IPPort = ofe.convertStringArray(jsExtractData["ip_port"])
	result.Domain = ofe.convertStringArray(jsExtractData["domain"])
	result.Path = ofe.convertStringArray(jsExtractData["path"])
	result.IncompletePath = ofe.convertStringArray(jsExtractData["incomplete_path"])
	result.URL = ofe.convertStringArray(jsExtractData["url"])
	result.SFZ = ofe.convertStringArray(jsExtractData["sfz"])
	result.Mobile = ofe.convertStringArray(jsExtractData["mobile"])
	result.Mail = ofe.convertStringArray(jsExtractData["mail"])
	result.JWT = ofe.convertStringArray(jsExtractData["jwt"])
	result.Algorithm = ofe.convertStringArray(jsExtractData["algorithm"])
	result.Secret = ofe.convertStringArray(jsExtractData["secret"])
	
	return result
}

// convertStringArray 转换JavaScript数组为Go字符串数组
func (ofe *OriginalFindSomethingExtractor) convertStringArray(jsArray interface{}) []string {
	if jsArray == nil {
		return nil
	}
	
	array, ok := jsArray.([]interface{})
	if !ok {
		return nil
	}
	
	var result []string
	for _, item := range array {
		if str, ok := item.(string); ok && str != "" {
			result = append(result, str)
		}
	}
	
	return result
}

// fillSourceMapping 填充敏感信息的来源映射
func (ofe *OriginalFindSomethingExtractor) fillSourceMapping(result *FindSomethingResult, source string) {
	// 为所有敏感信息项设置来源
	for _, item := range result.IP {
		result.Source[item] = source
	}
	for _, item := range result.IPPort {
		result.Source[item] = source
	}
	for _, item := range result.Domain {
		result.Source[item] = source
	}
	for _, item := range result.Path {
		result.Source[item] = source
	}
	for _, item := range result.IncompletePath {
		result.Source[item] = source
	}
	for _, item := range result.URL {
		result.Source[item] = source
	}
	for _, item := range result.SFZ {
		result.Source[item] = source
	}
	for _, item := range result.Mobile {
		result.Source[item] = source
	}
	for _, item := range result.Mail {
		result.Source[item] = source
	}
	for _, item := range result.JWT {
		result.Source[item] = source
	}
	for _, item := range result.Algorithm {
		result.Source[item] = source
	}
	for _, item := range result.Secret {
		result.Source[item] = source
	}
}
