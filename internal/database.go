package internal

import (
	"database/sql"
	"fmt"
	"strings"
	"time"
	"web_scanner/config"
	"web_scanner/core"

	_ "github.com/go-sql-driver/mysql"
	_ "github.com/lib/pq"
	_ "github.com/mattn/go-sqlite3"
)

// TransactionHandler 数据库事务处理器
// 提供统一的事务管理功能，包括自动回滚和错误处理
type TransactionHandler struct {
	tx *sql.Tx // 数据库事务实例
}

// NewTransactionHandler 创建新的事务处理器
// 参数：
//   - db: 数据库连接实例
//
// 返回值：
//   - *TransactionHandler: 事务处理器实例
//   - error: 创建过程中的错误
func NewTransactionHandler(db *sql.DB) (*TransactionHandler, error) {
	tx, err := db.Begin()
	if err != nil {
		return nil, err
	}
	return &TransactionHandler{tx: tx}, nil
}

// Prepare 准备SQL语句
// 参数：
//   - query: SQL查询语句
//
// 返回值：
//   - *sql.Stmt: 预编译的SQL语句
//   - error: 准备过程中的错误
func (th *TransactionHandler) Prepare(query string) (*sql.Stmt, error) {
	stmt, err := th.tx.Prepare(query)
	if err != nil {
		th.tx.Rollback()
		return nil, err
	}
	return stmt, nil
}

// Commit 提交事务
func (th *TransactionHandler) Commit() error {
	return th.tx.Commit()
}

// Rollback 回滚事务
func (th *TransactionHandler) Rollback() error {
	return th.tx.Rollback()
}

// SafeExecute 安全执行事务操作
// 自动处理panic和错误回滚
// 参数：
//   - fn: 要执行的函数
//
// 返回值：
//   - error: 执行过程中的错误
func (th *TransactionHandler) SafeExecute(fn func() error) error {
	defer func() {
		if p := recover(); p != nil {
			th.tx.Rollback()
			panic(p)
		}
	}()

	if err := fn(); err != nil {
		th.tx.Rollback()
		return err
	}

	return th.tx.Commit()
}

// ResourceCollector 资源收集器
// 用于收集和去重扫描结果中的资源和敏感信息
type ResourceCollector struct {
	resourceSet  map[string]ResourceInfo  // 资源集合，用于去重
	sensitiveSet map[string]SensitiveItem // 敏感信息集合，用于去重
	counters     map[string]int           // 计数器，用于统计各类型数量
}

// NewResourceCollector 创建新的资源收集器
// 返回值：
//   - *ResourceCollector: 资源收集器实例
func NewResourceCollector() *ResourceCollector {
	return &ResourceCollector{
		resourceSet:  make(map[string]ResourceInfo),
		sensitiveSet: make(map[string]SensitiveItem),
		counters:     make(map[string]int),
	}
}

// AddResource 添加资源到收集器
// 参数：
//   - taskID: 任务ID
//   - url: 资源URL
//   - resourceType: 资源类型（html, js, css, image, api, font, document, other）
func (rc *ResourceCollector) AddResource(taskID int64, url, resourceType string) {
	key := fmt.Sprintf("%d_%s_%s", taskID, resourceType, url)
	rc.resourceSet[key] = ResourceInfo{
		TaskID: taskID,
		URL:    url,
		Type:   resourceType,
	}
}

// AddSensitiveInfo 添加敏感信息到收集器，支持严格去重
// 使用任务ID、信息类型、值的组合作为唯一键进行去重
// 对于空值，使用特殊的键值以确保每种类型都有记录
// 参数：
//   - taskID: 任务ID
//   - infoType: 信息类型（ip, ip_port, domain, path等13种FindSomething类型）
//   - value: 敏感信息值（可以为空）
//   - source: 信息来源
func (rc *ResourceCollector) AddSensitiveInfo(taskID int64, infoType, value, source string) {
	// 对于空值，使用特殊的键值确保每种类型都有一条记录
	var key string
	if value == "" {
		key = fmt.Sprintf("%d_%s_NULL", taskID, infoType)
	} else {
		key = fmt.Sprintf("%d_%s_%s", taskID, infoType, value)
	}

	if _, exists := rc.sensitiveSet[key]; !exists {
		rc.sensitiveSet[key] = SensitiveItem{
			TaskID:   taskID,
			InfoType: infoType,
			Value:    value,
			Source:   source,
		}
	}
}

// GetResourceCount 获取资源总数
func (rc *ResourceCollector) GetResourceCount() int {
	return len(rc.resourceSet)
}

// GetSensitiveCount 获取敏感信息总数
func (rc *ResourceCollector) GetSensitiveCount() int {
	return len(rc.sensitiveSet)
}

// GetCountByType 获取指定类型的资源数量
// 参数：
//   - resourceType: 资源类型
//
// 返回值：
//   - int: 该类型资源的数量
func (rc *ResourceCollector) GetCountByType(resourceType string) int {
	count := 0
	for _, resource := range rc.resourceSet {
		if resource.Type == resourceType {
			count++
		}
	}
	return count
}

// ResourceInfo 资源信息结构体
// 用于存储扫描发现的各类资源信息，支持严格去重
type ResourceInfo struct {
	TaskID int64  // 任务ID，关联到scan_tasks表
	URL    string // 资源URL地址
	Type   string // 资源类型：html, js, css, image, api, font, document, other
}

// SensitiveItem 敏感信息项结构体
// 用于存储扫描发现的敏感信息，按任务ID、类型、值进行去重
type SensitiveItem struct {
	TaskID   int64  // 任务ID，关联到scan_tasks表
	InfoType string // 敏感信息类型：sfz, phone, email, ip, domain等
	Value    string // 敏感信息的具体值
	Source   string // 信息来源：html, js, api等
}

// DBStorage 数据库存储管理器
// 提供完整的扫描结果数据库存储功能，支持多项目数据隔离
type DBStorage struct {
	db            *sql.DB         // 数据库连接实例
	projectPrefix string          // 项目前缀，用于表名隔离
	dbManager     *core.DBManager // 数据库管理器
}

// 初始化数据库连接和表结构 - 使用统一的数据库管理器
func InitDBStorage(projectPrefix string) (*DBStorage, error) {
	if config.CurrentDBConfig == nil {
		return nil, fmt.Errorf("数据库配置未加载")
	}

	// 创建数据库管理器
	dbManager := core.NewDBManager()

	// 初始化数据库连接（使用统一的连接逻辑）
	if err := dbManager.InitConnection(config.CurrentDBConfig, projectPrefix); err != nil {
		return nil, fmt.Errorf("数据库连接初始化失败: %w", err)
	}

	// 获取数据库连接实例
	db := dbManager.GetDB()
	if db == nil {
		return nil, fmt.Errorf("获取数据库连接失败")
	}

	// 处理项目前缀
	if projectPrefix != "" {
		// 验证项目前缀格式（只允许字母、数字、下划线）
		if !isValidProjectPrefix(projectPrefix) {
			dbManager.Close()
			return nil, fmt.Errorf("项目前缀格式无效，只允许字母、数字、下划线: %s", projectPrefix)
		}
		projectPrefix = projectPrefix + "_"
	}

	storage := &DBStorage{
		db:            db,
		projectPrefix: projectPrefix,
		dbManager:     dbManager,
	}

	// 创建必要的表结构
	if err := storage.createTables(); err != nil {
		dbManager.Close()
		return nil, fmt.Errorf("创建表结构失败: %w", err)
	}

	return storage, nil
}

// isValidProjectPrefix 验证项目前缀格式
func isValidProjectPrefix(prefix string) bool {
	if len(prefix) == 0 || len(prefix) > 50 {
		return false
	}

	// 只允许字母、数字、下划线，且必须以字母开头
	for i, r := range prefix {
		if i == 0 {
			// 第一个字符必须是字母
			if !((r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z')) {
				return false
			}
		} else {
			// 其他字符可以是字母、数字、下划线
			if !((r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9') || r == '_') {
				return false
			}
		}
	}
	return true
}

// getTableName 获取带项目前缀的表名
func (s *DBStorage) getTableName(baseName string) string {
	return s.projectPrefix + baseName
}

// 关闭数据库连接 - 使用统一的数据库管理器
func (s *DBStorage) Close() error {
	if s.dbManager != nil {
		return s.dbManager.Close()
	}
	return nil
}

// 创建必要的表结构
func (s *DBStorage) createTables() error {
	// 根据数据库类型选择不同的表创建语句
	var idType, uniqueConstraint string
	if config.CurrentDBConfig.Driver == "sqlite3" {
		idType = "INTEGER PRIMARY KEY AUTOINCREMENT"
		uniqueConstraint = "UNIQUE(target_url)"
	} else {
		idType = "INT AUTO_INCREMENT PRIMARY KEY"
		uniqueConstraint = "UNIQUE KEY unique_url (target_url(767))"
	}

	// 创建扫描任务表（使用项目前缀）
	scanTasksTable := s.getTableName("scan_tasks")
	query := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s (
			id %s,
			target_url TEXT NOT NULL,
			start_time TIMESTAMP NOT NULL,
			end_time TIMESTAMP,
			status VARCHAR(50) NOT NULL,
			error_msg TEXT,
			html_count INT DEFAULT 0,
			js_count INT DEFAULT 0,
			css_count INT DEFAULT 0,
			image_count INT DEFAULT 0,
			api_count INT DEFAULT 0,
			font_count INT DEFAULT 0,
			document_count INT DEFAULT 0,
			other_count INT DEFAULT 0,
			sensitive_count INT DEFAULT 0,
			%s
		)
	`, scanTasksTable, idType, uniqueConstraint)

	_, err := s.db.Exec(query)
	if err != nil {
		return fmt.Errorf("创建%s表失败: %w", scanTasksTable, err)
	}

	// 创建资源链接表（使用项目前缀）
	resourceLinksTable := s.getTableName("resource_links")
	var resourceUniqueConstraint string
	if config.CurrentDBConfig.Driver == "sqlite3" {
		resourceUniqueConstraint = "UNIQUE(task_id, url, type)"
	} else {
		resourceUniqueConstraint = "UNIQUE KEY unique_resource (task_id, url(500), type(20))"
	}

	query = fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s (
			id %s,
			task_id INT NOT NULL,
			url TEXT NOT NULL,
			type VARCHAR(20) NOT NULL,
			FOREIGN KEY (task_id) REFERENCES %s(id),
			%s
		)
	`, resourceLinksTable, idType, scanTasksTable, resourceUniqueConstraint)

	_, err = s.db.Exec(query)
	if err != nil {
		return fmt.Errorf("创建%s表失败: %w", resourceLinksTable, err)
	}

	// 删除旧的js_sensitive_info表（如果存在）
	oldJsSensitiveInfoTable := s.getTableName("js_sensitive_info")
	dropOldTableQuery := fmt.Sprintf("DROP TABLE IF EXISTS %s", oldJsSensitiveInfoTable)
	_, err = s.db.Exec(dropOldTableQuery)
	if err != nil {
		return fmt.Errorf("删除旧的%s表失败: %w", oldJsSensitiveInfoTable, err)
	}

	// 创建新的FindSomething表（使用项目前缀）
	findSomethingTable := s.getTableName("findsomething")
	var sensitiveUniqueConstraint string
	if config.CurrentDBConfig.Driver == "sqlite3" {
		sensitiveUniqueConstraint = "UNIQUE(task_id, info_type, value, source)"
	} else {
		sensitiveUniqueConstraint = "UNIQUE KEY unique_findsomething_info (task_id, info_type(50), value(400), source(150))"
	}

	query = fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s (
			id %s,
			task_id INT NOT NULL,
			info_type VARCHAR(50) NOT NULL,
			value TEXT,
			source TEXT,
			FOREIGN KEY (task_id) REFERENCES %s(id),
			%s
		)
	`, findSomethingTable, idType, scanTasksTable, sensitiveUniqueConstraint)

	_, err = s.db.Exec(query)
	if err != nil {
		return fmt.Errorf("创建%s表失败: %w", findSomethingTable, err)
	}

	return nil
}

// BatchTaskResult 批量任务创建结果
type BatchTaskResult struct {
	URL    string
	TaskID int64
	Status string
	Error  error
}

// BatchCreateScanTasks 批量创建或获取扫描任务 - 高性能批量操作
// 功能说明：
//   - 批量处理大量URL，避免逐个插入的性能问题
//   - 自动检测已存在的任务，避免重复创建
//   - 使用数据库事务确保数据一致性
//   - 支持项目前缀，实现多项目数据隔离
//
// 参数
// 参数说明：
//   - urls: 待扫描的URL列表
//   - depth: 扫描深度
//
// jsInfoEnabled: 是否启用JS信息提取
//
//   - jsInfoEnabled: 是否启用JS信息提取
//
// 返回值：
//   - map[string]int64: URL到任务ID的映射
//   - error: 操作错误信息
func (s *DBStorage) BatchCreateScanTasks(urls []string, depth int, jsInfoEnabled bool) (map[string]int64, error) {
	if len(urls) == 0 {
		return make(map[string]int64), nil
	}

	// 在数据库操作前再次去重，确保绝对不会有重复URL
	uniqueURLs := make([]string, 0, len(urls))
	urlSet := make(map[string]bool)
	duplicateCount := 0

	for _, url := range urls {
		if !urlSet[url] {
			urlSet[url] = true
			uniqueURLs = append(uniqueURLs, url)
		} else {
			duplicateCount++
		}
	}

	// 如果发现重复，记录日志但继续处理
	if duplicateCount > 0 {
		fmt.Printf("⚠️ 数据库层面发现并跳过 %d 个重复URL\n", duplicateCount)
	}

	// 使用去重后的URL列表
	urls = uniqueURLs

	fmt.Printf("📊 开始批量创建扫描任务，共 %d 个唯一URL...\n", len(urls))
	startTime := time.Now()

	// 开始事务
	tx, err := s.db.Begin()
	if err != nil {
		return nil, fmt.Errorf("开始事务失败: %w", err)
	}
	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			panic(p)
		}
	}()

	scanTasksTable := s.getTableName("scan_tasks")
	taskIDs := make(map[string]int64)

	// 第一步：批量查询已存在的任务
	existingTasks := make(map[string]struct {
		ID     int64
		Status string
	})

	if len(urls) > 0 {
		// 构建批量查询语句
		placeholders := make([]string, len(urls))
		args := make([]interface{}, len(urls))
		for i, url := range urls {
			placeholders[i] = "?"
			args[i] = url
		}

		query := fmt.Sprintf("SELECT target_url, id, status FROM %s WHERE target_url IN (%s)",
			scanTasksTable, strings.Join(placeholders, ","))

		rows, err := tx.Query(query, args...)
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("批量查询已存在任务失败: %w", err)
		}
		defer rows.Close()

		for rows.Next() {
			var url string
			var id int64
			var status string
			if err := rows.Scan(&url, &id, &status); err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("扫描查询结果失败: %w", err)
			}
			existingTasks[url] = struct {
				ID     int64
				Status string
			}{ID: id, Status: status}
		}
	}

	fmt.Printf("✅ 发现 %d 个已存在的任务\n", len(existingTasks))

	// 第二步：处理已存在的任务
	var urlsToUpdate []string
	var taskIDsToUpdate []int64
	completedCount := 0

	for _, url := range urls {
		if task, exists := existingTasks[url]; exists {
			taskIDs[url] = task.ID
			if task.Status == "completed" {
				completedCount++
			} else {
				// 需要更新为running状态并清除旧数据
				urlsToUpdate = append(urlsToUpdate, url)
				taskIDsToUpdate = append(taskIDsToUpdate, task.ID)
			}
		}
	}

	// 批量更新已存在但未完成的任务
	if len(urlsToUpdate) > 0 {
		// 批量更新状态
		placeholders := make([]string, len(taskIDsToUpdate))
		args := make([]interface{}, len(taskIDsToUpdate)+2)
		args[0] = time.Now()
		args[1] = "running"
		for i, taskID := range taskIDsToUpdate {
			placeholders[i] = "?"
			args[i+2] = taskID
		}

		updateQuery := fmt.Sprintf("UPDATE %s SET start_time = ?, status = ?, error_msg = NULL WHERE id IN (%s)",
			scanTasksTable, strings.Join(placeholders, ","))
		_, err := tx.Exec(updateQuery, args...)
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("批量更新任务状态失败: %w", err)
		}

		// 批量清除旧数据
		if err := s.batchClearTaskData(tx, taskIDsToUpdate); err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("批量清除旧数据失败: %w", err)
		}

		fmt.Printf("✅ 更新 %d 个未完成的任务\n", len(urlsToUpdate))
	}

	// 第三步：批量插入新任务
	var newURLs []string
	for _, url := range urls {
		if _, exists := existingTasks[url]; !exists {
			newURLs = append(newURLs, url)
		}
	}

	if len(newURLs) > 0 {
		// 准备批量插入语句
		valueStrings := make([]string, len(newURLs))
		valueArgs := make([]interface{}, len(newURLs)*3)
		now := time.Now()

		for i, url := range newURLs {
			valueStrings[i] = "(?, ?, ?)"
			valueArgs[i*3] = url
			valueArgs[i*3+1] = now
			valueArgs[i*3+2] = "running"
		}

		insertQuery := fmt.Sprintf("INSERT INTO %s (target_url, start_time, status) VALUES %s",
			scanTasksTable, strings.Join(valueStrings, ","))

		result, err := tx.Exec(insertQuery, valueArgs...)
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("批量插入新任务失败: %w", err)
		}

		// 获取插入的第一个ID
		firstID, err := result.LastInsertId()
		if err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("获取插入ID失败: %w", err)
		}

		// 为新插入的任务分配ID
		for i, url := range newURLs {
			taskIDs[url] = firstID + int64(i)
		}

		fmt.Printf("✅ 创建 %d 个新任务\n", len(newURLs))
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	duration := time.Since(startTime)
	fmt.Printf("🚀 批量任务创建完成！耗时: %v (已完成: %d, 需扫描: %d)\n",
		duration, completedCount, len(urls)-completedCount)

	return taskIDs, nil
}

// GetOrCreateScanTask 获取或创建扫描任务，返回任务ID和状态
func (s *DBStorage) GetOrCreateScanTask(targetURL string, depth int, jsInfoEnabled bool) (int64, string, error) {
	// 先查找是否存在相同的URL任务
	var taskID int64
	var status string
	scanTasksTable := s.getTableName("scan_tasks")
	query := fmt.Sprintf("SELECT id, status FROM %s WHERE target_url = ?", scanTasksTable)
	err := s.db.QueryRow(query, targetURL).Scan(&taskID, &status)
	if err == nil {
		// 如果找到了且状态为completed，直接返回
		if status == "completed" {
			return taskID, status, nil
		}

		// 如果状态不是completed，则更新为running
		query = fmt.Sprintf("UPDATE %s SET start_time = ?, status = ?, error_msg = NULL WHERE id = ?", scanTasksTable)
		_, err = s.db.Exec(query, time.Now(), "running", taskID)
		if err != nil {
			return 0, "", fmt.Errorf("更新已存在的任务失败: %w", err)
		}

		// 清除旧的资源链接和敏感信息
		if err := s.clearTaskData(taskID); err != nil {
			return 0, "", fmt.Errorf("清除旧数据失败: %w", err)
		}

		return taskID, "running", nil
	}

	// 如果不存在，创建新任务
	query = fmt.Sprintf("INSERT INTO %s (target_url, start_time, status) VALUES (?, ?, ?)", scanTasksTable)
	result, err := s.db.Exec(query, targetURL, time.Now(), "running")
	if err != nil {
		return 0, "", err
	}

	taskID, err = result.LastInsertId()
	if err != nil {
		return 0, "", err
	}

	return taskID, "running", nil
}

// 创建新的扫描任务，如果URL已存在则更新现有任务
func (s *DBStorage) CreateScanTask(targetURL string, depth int, jsInfoEnabled bool) (int64, error) {
	taskID, _, err := s.GetOrCreateScanTask(targetURL, depth, jsInfoEnabled)
	return taskID, err
}

// 清除任务相关的数据
func (s *DBStorage) clearTaskData(taskID int64) error {
	// 开始事务
	tx, err := s.db.Begin()
	if err != nil {
		return err
	}
	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			panic(p)
		}
	}()

	// 删除资源链接
	resourceLinksTable := s.getTableName("resource_links")
	query := fmt.Sprintf("DELETE FROM %s WHERE task_id = ?", resourceLinksTable)
	_, err = tx.Exec(query, taskID)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 删除敏感信息
	jsSensitiveInfoTable := s.getTableName("js_sensitive_info")
	query = fmt.Sprintf("DELETE FROM %s WHERE task_id = ?", jsSensitiveInfoTable)
	_, err = tx.Exec(query, taskID)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 重置资源计数
	scanTasksTable := s.getTableName("scan_tasks")
	query = fmt.Sprintf(`
		UPDATE %s SET
		html_count = 0,
		js_count = 0,
		css_count = 0,
		image_count = 0,
		api_count = 0,
		other_count = 0,
		sensitive_count = 0
		WHERE id = ?
	`, scanTasksTable)
	_, err = tx.Exec(query, taskID)
	if err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit()
}

// batchClearTaskData 批量清除任务的旧数据（资源链接和敏感信息）
func (s *DBStorage) batchClearTaskData(tx *sql.Tx, taskIDs []int64) error {
	if len(taskIDs) == 0 {
		return nil
	}

	// 构建批量删除的占位符
	placeholders := make([]string, len(taskIDs))
	args := make([]interface{}, len(taskIDs))
	for i, taskID := range taskIDs {
		placeholders[i] = "?"
		args[i] = taskID
	}
	placeholderStr := strings.Join(placeholders, ",")

	// 批量删除资源链接
	resourceLinksTable := s.getTableName("resource_links")
	query := fmt.Sprintf("DELETE FROM %s WHERE task_id IN (%s)", resourceLinksTable, placeholderStr)
	_, err := tx.Exec(query, args...)
	if err != nil {
		return fmt.Errorf("批量删除资源链接失败: %w", err)
	}

	// 批量删除敏感信息
	findSomethingTable := s.getTableName("findsomething")
	query = fmt.Sprintf("DELETE FROM %s WHERE task_id IN (%s)", findSomethingTable, placeholderStr)
	_, err = tx.Exec(query, args...)
	if err != nil {
		return fmt.Errorf("批量删除敏感信息失败: %w", err)
	}

	// 批量重置资源计数
	scanTasksTable := s.getTableName("scan_tasks")
	query = fmt.Sprintf(`
		UPDATE %s SET
		html_count = 0,
		js_count = 0,
		css_count = 0,
		image_count = 0,
		api_count = 0,
		other_count = 0,
		sensitive_count = 0
		WHERE id IN (%s)
	`, scanTasksTable, placeholderStr)
	_, err = tx.Exec(query, args...)
	if err != nil {
		return fmt.Errorf("批量重置资源计数失败: %w", err)
	}

	return nil
}

// GetTaskStatus 获取任务状态
func (s *DBStorage) GetTaskStatus(taskID int64) (string, error) {
	var status string
	scanTasksTable := s.getTableName("scan_tasks")
	query := fmt.Sprintf("SELECT status FROM %s WHERE id = ?", scanTasksTable)
	err := s.db.QueryRow(query, taskID).Scan(&status)
	if err != nil {
		return "", fmt.Errorf("获取任务状态失败: %w", err)
	}
	return status, nil
}

// 完成扫描任务
func (s *DBStorage) CompleteScanTask(taskID int64) error {
	return s.UpdateTaskStatus(taskID, "completed", "")
}

// SaveScanResults 保存扫描结果到数据库
// 使用事务确保数据一致性，自动去重相同的资源和敏感信息
// 参数：
//   - taskID: 任务ID
//   - results: 扫描结果列表
//
// 返回值：
//   - error: 保存过程中的错误
func (s *DBStorage) SaveScanResults(taskID int64, results []ScanResult) error {
	_, _, err := s.SaveScanResultsWithStats(taskID, results)
	return err
}

// SaveScanResultsWithStats 保存扫描结果并返回统计信息
func (s *DBStorage) SaveScanResultsWithStats(taskID int64, results []ScanResult) (int, int, error) {
	th, err := NewTransactionHandler(s.db)
	if err != nil {
		return 0, 0, err
	}

	var resourceCount, sensitiveCount int
	err = th.SafeExecute(func() error {
		resourceLinksTable := s.getTableName("resource_links")
		resourceQuery := fmt.Sprintf("REPLACE INTO %s (task_id, url, type) VALUES (?, ?, ?)", resourceLinksTable)
		stmtResource, err := th.Prepare(resourceQuery)
		if err != nil {
			return err
		}
		defer stmtResource.Close()

		findSomethingTable := s.getTableName("findsomething")
		sensitiveQuery := fmt.Sprintf("REPLACE INTO %s (task_id, info_type, value, source) VALUES (?, ?, ?, ?)", findSomethingTable)
		stmtSensitive, err := th.Prepare(sensitiveQuery)
		if err != nil {
			return err
		}
		defer stmtSensitive.Close()

		collector := NewResourceCollector()
		err = s.processResults(taskID, results, collector, stmtResource, stmtSensitive, th)
		if err != nil {
			return err
		}

		// 获取统计信息
		resourceCount = collector.GetResourceCount()
		sensitiveCount = collector.GetSensitiveCount()
		return nil
	})

	return resourceCount, sensitiveCount, err
}

// processResults 处理扫描结果
// 收集资源和敏感信息，然后保存到数据库并更新统计信息
func (s *DBStorage) processResults(taskID int64, results []ScanResult, collector *ResourceCollector, stmtResource, stmtSensitive *sql.Stmt, th *TransactionHandler) error {

	for _, result := range results {
		s.collectResources(taskID, result, collector)
		s.collectSensitiveInfo(taskID, result, collector)
	}

	if err := s.saveCollectedData(collector, stmtResource, stmtSensitive); err != nil {
		return err
	}

	return s.updateTaskStatistics(taskID, collector, th)
}

// collectResources 收集扫描结果中的资源信息
// 将各类型资源添加到收集器中进行统一管理和去重
func (s *DBStorage) collectResources(taskID int64, result ScanResult, collector *ResourceCollector) {
	for _, url := range result.HTMLList {
		collector.AddResource(taskID, url, "html")
	}
	for _, url := range result.JSList {
		collector.AddResource(taskID, url, "js")
	}
	for _, url := range result.CSSList {
		collector.AddResource(taskID, url, "css")
	}
	for _, url := range result.ImageList {
		collector.AddResource(taskID, url, "image")
	}
	for _, url := range result.FontList {
		collector.AddResource(taskID, url, "font")
	}
	for _, url := range result.DocList {
		collector.AddResource(taskID, url, "document")
	}
	for _, url := range result.OtherList {
		collector.AddResource(taskID, url, "other")
	}
	for _, api := range result.APIList {
		collector.AddResource(taskID, api.URL, "api")
	}
}



// saveCollectedData 保存收集的数据到数据库
// 将收集器中的资源和敏感信息批量保存到数据库
// 参数：
//   - collector: 资源收集器
//   - stmtResource: 资源插入语句
//   - stmtSensitive: 敏感信息插入语句
//
// 返回值：
//   - error: 保存过程中的错误
func (s *DBStorage) saveCollectedData(collector *ResourceCollector, stmtResource, stmtSensitive *sql.Stmt) error {
	// 保存资源信息
	for _, resource := range collector.resourceSet {
		if _, err := stmtResource.Exec(resource.TaskID, resource.URL, resource.Type); err != nil {
			return fmt.Errorf("保存资源失败: %w", err)
		}
	}

	// 保存敏感信息
	for _, sensitive := range collector.sensitiveSet {
		if _, err := stmtSensitive.Exec(sensitive.TaskID, sensitive.InfoType, sensitive.Value, sensitive.Source); err != nil {
			return fmt.Errorf("保存敏感信息失败: %w", err)
		}
	}

	// 保存统计信息已在主程序中通过outputManager显示

	return nil
}

// updateTaskStatistics 更新任务统计信息
// 根据收集器中的数据更新任务表中的各类资源和敏感信息计数
// 参数：
//   - taskID: 任务ID
//   - collector: 资源收集器
//   - th: 事务处理器
//
// 返回值：
//   - error: 更新过程中的错误
func (s *DBStorage) updateTaskStatistics(taskID int64, collector *ResourceCollector, th *TransactionHandler) error {
	scanTasksTable := s.getTableName("scan_tasks")
	updateQuery := fmt.Sprintf(`
		UPDATE %s SET
		html_count = ?,
		js_count = ?,
		css_count = ?,
		image_count = ?,
		api_count = ?,
		font_count = ?,
		document_count = ?,
		other_count = ?,
		sensitive_count = ?
		WHERE id = ?
	`, scanTasksTable)

	_, err := th.tx.Exec(updateQuery,
		collector.GetCountByType("html"),
		collector.GetCountByType("js"),
		collector.GetCountByType("css"),
		collector.GetCountByType("image"),
		collector.GetCountByType("api"),
		collector.GetCountByType("font"),
		collector.GetCountByType("document"),
		collector.GetCountByType("other"),
		collector.GetSensitiveCount(),
		taskID)

	return err
}

// collectSensitiveInfo 收集扫描结果中的敏感信息
// 处理各类敏感信息并添加到收集器中，完全按照FindSomething的13种数据类型
// 参数：
//   - taskID: 任务ID
//   - result: 扫描结果
//   - collector: 资源收集器
func (s *DBStorage) collectSensitiveInfo(taskID int64, result ScanResult, collector *ResourceCollector) {
	// FindSomething的13种类型：["ip","ip_port","domain","path","incomplete_path","url","sfz","mobile","mail","jwt","algorithm","secret","static"]
	findSomethingTypes := []string{"ip", "ip_port", "domain", "path", "incomplete_path", "url", "sfz", "mobile", "mail", "jwt", "algorithm", "secret", "static"}

	// 为每种类型创建一条记录，即使没有数据也保存null值
	for _, infoType := range findSomethingTypes {
		if result.SensitiveInfo == nil || result.SensitiveInfo.FindSomethingData == nil {
			// 没有敏感信息数据，保存null记录
			collector.AddSensitiveInfo(taskID, infoType, "", result.URL)
			continue
		}

		fsData := result.SensitiveInfo.FindSomethingData

		// 根据类型处理对应的数据
		switch infoType {
		case "ip":
			s.addFindSomethingStringItems(taskID, fsData.IP, "ip", fsData.Source, collector)
		case "ip_port":
			s.addFindSomethingStringItems(taskID, fsData.IPPort, "ip_port", fsData.Source, collector)
		case "domain":
			// 域名需要额外验证
			hasValidDomain := false
			for _, value := range fsData.Domain {
				if isValidDomainForDB(value) {
					source := fsData.Source[value]
					if source == "" {
						source = fsData.Current
					}
					collector.AddSensitiveInfo(taskID, "domain", value, source)
					hasValidDomain = true
				}
			}
			if !hasValidDomain {
				collector.AddSensitiveInfo(taskID, "domain", "", result.URL)
			}
		case "path":
			s.addFindSomethingStringItems(taskID, fsData.Path, "path", fsData.Source, collector)
		case "incomplete_path":
			s.addFindSomethingStringItems(taskID, fsData.IncompletePath, "incomplete_path", fsData.Source, collector)
		case "url":
			s.addFindSomethingStringItems(taskID, fsData.URL, "url", fsData.Source, collector)
		case "sfz":
			s.addFindSomethingStringItems(taskID, fsData.SFZ, "sfz", fsData.Source, collector)
		case "mobile":
			s.addFindSomethingStringItems(taskID, fsData.Mobile, "mobile", fsData.Source, collector)
		case "mail":
			s.addFindSomethingStringItems(taskID, fsData.Mail, "mail", fsData.Source, collector)
		case "jwt":
			s.addFindSomethingStringItems(taskID, fsData.JWT, "jwt", fsData.Source, collector)
		case "algorithm":
			s.addFindSomethingStringItems(taskID, fsData.Algorithm, "algorithm", fsData.Source, collector)
		case "secret":
			s.addFindSomethingStringItems(taskID, fsData.Secret, "secret", fsData.Source, collector)
		case "static":
			// static类型暂时保存为空，后续可以从resource_links表中获取静态资源
			collector.AddSensitiveInfo(taskID, "static", "", result.URL)
		}
	}
}

// addFindSomethingStringItems 添加FindSomething格式的字符串项到收集器
// 内部辅助函数，用于处理FindSomething数据结构中的字符串数组
// 参数：
//   - taskID: 任务ID
//   - items: 字符串数组
//   - infoType: 信息类型
//   - sourceMap: 来源映射
//   - collector: 资源收集器
func (s *DBStorage) addFindSomethingStringItems(taskID int64, items []string, infoType string, sourceMap map[string]string, collector *ResourceCollector) {
	if len(items) == 0 {
		// 如果没有数据，添加一条空值记录
		source := sourceMap["current"]
		if source == "" {
			source = "unknown"
		}
		collector.AddSensitiveInfo(taskID, infoType, "", source)
		return
	}

	for _, value := range items {
		// 获取来源信息
		source := sourceMap[value]
		if source == "" {
			// 如果没有找到特定来源，使用当前URL作为默认来源
			source = sourceMap["current"]
			if source == "" {
				for _, src := range sourceMap {
					source = src
					break
				}
			}
		}

		// 添加到收集器
		collector.AddSensitiveInfo(taskID, infoType, value, source)
	}
}

// UpdateTaskStatus 更新任务状态
func (s *DBStorage) UpdateTaskStatus(taskID int64, status string, errorMsg string) error {
	scanTasksTable := s.getTableName("scan_tasks")
	var query string
	var args []interface{}

	if errorMsg != "" {
		// 如果有错误信息，添加到数据库中
		query = fmt.Sprintf("UPDATE %s SET end_time = ?, status = ?, error_msg = ? WHERE id = ?", scanTasksTable)
		args = []interface{}{time.Now(), status, errorMsg, taskID}
	} else {
		query = fmt.Sprintf("UPDATE %s SET end_time = ?, status = ? WHERE id = ?", scanTasksTable)
		args = []interface{}{time.Now(), status, taskID}
	}

	_, err := s.db.Exec(query, args...)
	return err
}

// ResetDatabase 重置数据库表结构（只重置当前项目的表）
func (s *DBStorage) ResetDatabase() error {
	// 定义当前项目的表名
	projectTables := []string{
		s.getTableName("scan_tasks"),
		s.getTableName("resource_links"),
		s.getTableName("findsomething"),
		s.getTableName("js_sensitive_info"), // 保留旧表名以防万一
	}

	// 根据数据库类型处理外键约束
	if config.CurrentDBConfig.Driver != "sqlite3" {
		// MySQL/PostgreSQL: 禁用外键约束检查
		_, err := s.db.Exec("SET FOREIGN_KEY_CHECKS = 0")
		if err != nil {
			return err
		}
		defer s.db.Exec("SET FOREIGN_KEY_CHECKS = 1")
	} else {
		// SQLite: 禁用外键约束
		_, err := s.db.Exec("PRAGMA foreign_keys = OFF")
		if err != nil {
			return err
		}
		defer s.db.Exec("PRAGMA foreign_keys = ON")
	}

	// 删除当前项目的表
	for _, table := range projectTables {
		query := fmt.Sprintf("DROP TABLE IF EXISTS %s", table)
		_, err := s.db.Exec(query)
		if err != nil {
			return fmt.Errorf("删除表 %s 失败: %w", table, err)
		}
	}

	// 重新创建表结构
	return s.createTables()
}

// isValidDomainForDB 在数据库保存阶段验证域名，确保没有文件名被保存
func isValidDomainForDB(domain string) bool {
	// 基本长度检查
	if len(domain) < 4 || len(domain) > 253 {
		return false
	}

	// 必须包含至少一个点
	if !strings.Contains(domain, ".") {
		return false
	}

	lowerDomain := strings.ToLower(domain)

	// 检查是否是文件名（最严格的检查）
	fileExtensions := []string{
		".html", ".htm", ".php", ".asp", ".aspx", ".jsp", ".js", ".css", ".json", ".xml",
		".txt", ".log", ".md", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
		".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg", ".ico", ".webp",
		".mp3", ".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".ogg", ".wav",
		".zip", ".rar", ".tar", ".gz", ".7z", ".exe", ".msi", ".dmg", ".deb", ".rpm",
		".sql", ".db", ".sqlite", ".mdb", ".csv", ".tsv",
		".py", ".java", ".cpp", ".c", ".h", ".go", ".rs", ".rb", ".pl", ".sh",
		".min.js", ".min.css", ".bundle.js", ".chunk.js", ".map",
	}

	// 检查是否以文件扩展名结尾
	for _, ext := range fileExtensions {
		if strings.HasSuffix(lowerDomain, ext) {
			return false
		}
	}

	// 检查是否包含典型的文件名模式
	filePatterns := []string{
		"index.", "main.", "app.", "script.", "style.", "config.", "settings.",
		"readme.", "license.", "changelog.", "package.", "manifest.",
	}

	for _, pattern := range filePatterns {
		if strings.Contains(lowerDomain, pattern) {
			return false
		}
	}

	// 必须至少包含一个有效的TLD
	parts := strings.Split(lowerDomain, ".")
	if len(parts) < 2 {
		return false
	}

	// 检查TLD是否有效（最后一个部分）
	tld := parts[len(parts)-1]
	if len(tld) < 2 || len(tld) > 10 {
		return false
	}

	// TLD只能包含字母
	for _, r := range tld {
		if r < 'a' || r > 'z' {
			return false
		}
	}

	return true
}
